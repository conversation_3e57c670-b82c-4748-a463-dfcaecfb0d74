// ===== GAME STATE MANAGEMENT =====
// Centralized state management with event dispatching

class GameState {
    constructor() {
        // Core game state
        this.player = {
            x: CONSTANTS.PLAYER_START_X,
            y: CONSTANTS.PLAYER_START_Y,
            direction: CONSTANTS.DIRECTIONS.NORTH,
            floor: 1
        };
        
        // Game statistics
        this.currentFloor = 1;
        this.turnCounter = 0;
        this.enemiesDefeated = 0;
        this.stepsTaken = 0;
        this.enemyRespawnTimer = CONSTANTS.ENEMY.RESPAWN_TIMER;
        this.enemyRespawnCount = 0;
        this.gold = 0; // Party gold
        
        // Game flags
        this.inCombat = false;
        this.hasMappingTool = false;
        
        // Smoke screen state
        this.smokeScreenActive = false;
        this.smokeScreenStepsRemaining = 0;
        
        // Game data
        this.party = [];
        this.dungeon = [];
        this.floors = [];
        
        // Inventory system - party-wide inventory
        this.inventory = {
            health_potion: 3,  // Start with 3 health potions
            smoke_screen: 2,   // Start with 2 smoke screens
            antidote: 1,       // Start with 1 antidote
            orb: 2,           // Start with 5 ORBs for testing ALCHEMIST shop
            mystic_orb: 1     // Start with 2 MYSTIC ORBs for testing
        };

        // Enhancement Skills System (ALCHEMIST shop)
        this.ownedSkills = [];  // Array of owned enhancement skills
        this.equippedSkills = {  // Object mapping character names to equipped skills
            // Each character can have up to 4 equipped skills
            // Format: { "Warrior": ["life_enhancement", "str_enhancement"], ... }
        };
        
        // Canvas references
        this.canvas = null;
        this.ctx = null;
        
        // Event listeners for state changes
        this.listeners = {};
    }
    
    // Event system for component communication
    addEventListener(event, callback) {
        if (!this.listeners[event]) {
            this.listeners[event] = [];
        }
        this.listeners[event].push(callback);
    }
    
    removeEventListener(event, callback) {
        if (this.listeners[event]) {
            this.listeners[event] = this.listeners[event].filter(cb => cb !== callback);
        }
    }
    
    dispatchEvent(event, data = {}) {
        if (this.listeners[event]) {
            this.listeners[event].forEach(callback => callback(data));
        }
    }
    
    // Player state management
    setPlayerPosition(x, y) {
        this.player.x = x;
        this.player.y = y;
        this.dispatchEvent(CONSTANTS.EVENTS.PLAYER_MOVED, { x, y });
    }
    
    setPlayerDirection(direction) {
        this.player.direction = direction;
        this.dispatchEvent(CONSTANTS.EVENTS.PLAYER_MOVED, { 
            x: this.player.x, 
            y: this.player.y, 
            direction 
        });
    }
    
    // Game statistics
    incrementSteps() {
        this.stepsTaken++;
        this.enemyRespawnTimer--;
        
        // Decrement smoke screen duration if active
        this.decrementSmokeScreenDuration();
        
        this.dispatchEvent(CONSTANTS.EVENTS.GAME_STATE_CHANGED);
    }
    
    incrementTurn() {
        this.turnCounter++;
        this.dispatchEvent(CONSTANTS.EVENTS.GAME_STATE_CHANGED);
    }
    
    incrementEnemiesDefeated() {
        this.enemiesDefeated++;
        this.dispatchEvent(CONSTANTS.EVENTS.GAME_STATE_CHANGED);
    }
    
    resetEnemyRespawnTimer() {
        this.enemyRespawnTimer = CONSTANTS.ENEMY.RESPAWN_TIMER;
        this.dispatchEvent(CONSTANTS.EVENTS.GAME_STATE_CHANGED);
    }
    
    // Gold management
    addGold(amount) {
        this.gold += amount;
        this.dispatchEvent(CONSTANTS.EVENTS.GAME_STATE_CHANGED);
    }
    
    getGold() {
        return this.gold;
    }
    
    // Combat state
    setCombatState(inCombat) {
        this.inCombat = inCombat;
        if (inCombat) {
            this.dispatchEvent(CONSTANTS.EVENTS.COMBAT_STARTED);
        } else {
            this.dispatchEvent(CONSTANTS.EVENTS.COMBAT_ENDED);
        }
    }
    
    // Floor management
    setCurrentFloor(floor) {
        this.currentFloor = floor;
        this.player.floor = floor;
        this.dispatchEvent(CONSTANTS.EVENTS.FLOOR_CHANGED, { floor });
    }
    
    // Canvas management
    setCanvas(canvas) {
        this.canvas = canvas;
        this.ctx = canvas.getContext('2d');
    }
    
    // Party management
    setParty(party) {
        this.party = party;
        this.dispatchEvent(CONSTANTS.EVENTS.GAME_STATE_CHANGED);
    }
    
    // Dungeon management
    setDungeon(dungeon) {
        this.dungeon = dungeon;
        this.dispatchEvent(CONSTANTS.EVENTS.GAME_STATE_CHANGED);
    }
    
    setFloors(floors) {
        this.floors = floors;
        this.dispatchEvent(CONSTANTS.EVENTS.GAME_STATE_CHANGED);
    }
    
    // Utility methods
    getCurrentCell() {
        return this.dungeon[this.player.y][this.player.x];
    }
    
    // Get the position the player is facing
    getFacingPosition() {
        const dir = this.getCurrentDirectionVector();
        return {
            x: this.player.x + dir.x,
            y: this.player.y + dir.y
        };
    }
    
    // Get the cell the player is facing
    getFacingCell() {
        const pos = this.getFacingPosition();
        if (pos.x < 0 || pos.x >= CONSTANTS.DUNGEON_SIZE || 
            pos.y < 0 || pos.y >= CONSTANTS.DUNGEON_SIZE) {
            return null;
        }
        return this.dungeon[pos.y][pos.x];
    }
    
    isValidPosition(x, y) {
        if (x < 0 || x >= CONSTANTS.DUNGEON_SIZE || y < 0 || y >= CONSTANTS.DUNGEON_SIZE) {
            return false;
        }
        const cell = this.dungeon[y][x];
        
        // Cannot move into walls
        if (cell.type === 'wall') {
            return false;
        }
        
        // Cannot move into closed doors
        if (cell.type === 'door' && !cell.opened) {
            return false;
        }
        
        return true;
    }
    
    getDepthName() {
        if (this.currentFloor <= 3) return 'Shallow';
        if (this.currentFloor <= 7) return 'Deep';
        return 'Abyssal';
    }
    
    // Get alive party members
    getAlivePartyMembers() {
        return this.party.filter(member => member.life > 0);
    }
    
    // Check if game should end
    isGameOver() {
        return this.getAlivePartyMembers().length === 0;
    }

    // Enhancement Skills Management
    addOwnedSkill(skillId) {
        if (!this.ownedSkills.includes(skillId)) {
            this.ownedSkills.push(skillId);
            this.dispatchEvent(CONSTANTS.EVENTS.GAME_STATE_CHANGED);
        }
    }

    getOwnedSkills() {
        return [...this.ownedSkills];
    }

    equipSkillToCharacter(characterName, skillId) {
        if (!this.equippedSkills[characterName]) {
            this.equippedSkills[characterName] = [];
        }

        // Check if character already has 4 skills equipped
        if (this.equippedSkills[characterName].length >= 4) {
            return false; // Cannot equip more than 4 skills
        }

        // Check if skill is already equipped
        if (this.equippedSkills[characterName].includes(skillId)) {
            return false; // Already equipped
        }

        this.equippedSkills[characterName].push(skillId);
        this.dispatchEvent(CONSTANTS.EVENTS.GAME_STATE_CHANGED);
        return true;
    }

    unequipSkillFromCharacter(characterName, skillId) {
        if (this.equippedSkills[characterName]) {
            const index = this.equippedSkills[characterName].indexOf(skillId);
            if (index !== -1) {
                this.equippedSkills[characterName].splice(index, 1);
                this.dispatchEvent(CONSTANTS.EVENTS.GAME_STATE_CHANGED);
                return true;
            }
        }
        return false;
    }

    getEquippedSkills(characterName) {
        return this.equippedSkills[characterName] || [];
    }

    // Calculate enhanced stats for a character based on equipped skills
    getEnhancedStats(character) {
        const equippedSkills = this.getEquippedSkills(character.name);
        const enhancedStats = {
            maxLife: character.maxLife,
            str: character.str,
            int: character.int,
            agl: character.agl
        };

        // Apply enhancement skill bonuses
        equippedSkills.forEach(skillId => {
            const skillData = CONSTANTS.ENHANCEMENT_SKILLS[skillId.toUpperCase()];
            if (skillData) {
                switch (skillData.effect) {
                    case 'maxLife':
                        enhancedStats.maxLife = Math.floor(enhancedStats.maxLife * skillData.multiplier);
                        break;
                    case 'str':
                        enhancedStats.str = Math.floor(enhancedStats.str * skillData.multiplier);
                        break;
                    case 'int':
                        enhancedStats.int = Math.floor(enhancedStats.int * skillData.multiplier);
                        break;
                    case 'agl':
                        enhancedStats.agl = Math.floor(enhancedStats.agl * skillData.multiplier);
                        break;
                }
            }
        });

        return enhancedStats;
    }
    
    // Get current direction vector
    getCurrentDirectionVector() {
        return CONSTANTS.DIRECTION_VECTORS[this.player.direction];
    }
    
    // Get left and right direction vectors for rendering
    getLeftDirectionVector() {
        return CONSTANTS.DIRECTION_VECTORS[(this.player.direction + 3) % 4];
    }
    
    getRightDirectionVector() {
        return CONSTANTS.DIRECTION_VECTORS[(this.player.direction + 1) % 4];
    }
    
    // Mapping tool management
    obtainMappingTool() {
        if (!this.hasMappingTool) {
            console.log('Mapping tool obtained! Triggering notification...');
            this.hasMappingTool = true;
            this.dispatchEvent(CONSTANTS.EVENTS.MAPPING_TOOL_OBTAINED);
            this.dispatchEvent(CONSTANTS.EVENTS.GAME_STATE_CHANGED);
        }
    }
    
    // Smoke screen management
    activateSmokeScreen(duration) {
        this.smokeScreenActive = true;
        this.smokeScreenStepsRemaining = duration;
        this.dispatchEvent(CONSTANTS.EVENTS.GAME_STATE_CHANGED);
    }
    
    decrementSmokeScreenDuration() {
        if (this.smokeScreenActive && this.smokeScreenStepsRemaining > 0) {
            this.smokeScreenStepsRemaining--;
            if (this.smokeScreenStepsRemaining <= 0) {
                this.deactivateSmokeScreen();
            }
            this.dispatchEvent(CONSTANTS.EVENTS.GAME_STATE_CHANGED);
        }
    }
    
    deactivateSmokeScreen() {
        this.smokeScreenActive = false;
        this.smokeScreenStepsRemaining = 0;
        this.dispatchEvent(CONSTANTS.EVENTS.GAME_STATE_CHANGED);
    }
    
    isSmokeScreenActive() {
        return this.smokeScreenActive;
    }
    
    // Inventory management
    addItem(itemId, quantity = 1) {
        const item = CONSTANTS.ITEMS[itemId.toUpperCase()];
        if (!item) {
            console.error(`Unknown item: ${itemId}`);
            return false;
        }

        // Check if we can add this item
        if (!this.canAddItem(itemId, quantity)) {
            return false;
        }

        // Add to inventory
        if (!this.inventory[itemId]) {
            this.inventory[itemId] = 0;
        }

        this.inventory[itemId] += quantity;

        // Check if this is a health potion acquisition and show overlay
        if (itemId.toLowerCase() === 'health_potion' && window.healthPotionNotification) {
            window.healthPotionNotification.showNotification();
        }

        // Check if this is an orb acquisition and show overlay
        if (itemId.toLowerCase() === 'orb' && window.orbNotification) {
            window.orbNotification.showNotification();
        }

        // Check if this is a mystic orb acquisition and show overlay
        if (itemId.toLowerCase() === 'mystic_orb' && window.mysticOrbNotification) {
            window.mysticOrbNotification.showNotification();
        }

        // Check if this is a smoke screen acquisition and show overlay
        if (itemId.toLowerCase() === 'smoke_screen' && window.smokeScreenNotification) {
            window.smokeScreenNotification.showNotification();
        }

        // Check if this is an antidote acquisition and show overlay
        if (itemId.toLowerCase() === 'antidote' && window.antidoteNotification) {
            window.antidoteNotification.showNotification();
        }

        this.dispatchEvent(CONSTANTS.EVENTS.GAME_STATE_CHANGED);
        return true;
    }
    
    removeItem(itemId, quantity = 1) {
        if (!this.inventory[itemId] || this.inventory[itemId] < quantity) {
            return false;
        }
        
        this.inventory[itemId] -= quantity;
        
        // Remove from inventory if count reaches 0
        if (this.inventory[itemId] === 0) {
            delete this.inventory[itemId];
        }
        
        this.dispatchEvent(CONSTANTS.EVENTS.GAME_STATE_CHANGED);
        return true;
    }
    
    getItemCount(itemId) {
        return this.inventory[itemId] || 0;
    }
    
    getTotalItemCount() {
        return Object.values(this.inventory).reduce((total, count) => total + count, 0);
    }
    
    canAddItem(itemId, quantity = 1) {
        const item = CONSTANTS.ITEMS[itemId.toUpperCase()];
        if (!item) return false;
        
        const currentCount = this.getItemCount(itemId);
        const newCount = currentCount + quantity;
        
        // Check item stack limit
        if (newCount > item.maxStack) {
            return false;
        }
        
        // Check total inventory slots
        const totalAfterAdd = this.getTotalItemCount() + quantity;
        if (totalAfterAdd > CONSTANTS.INVENTORY.MAX_TOTAL_SLOTS) {
            return false;
        }
        
        return true;
    }
    
    getInventoryItems() {
        const items = [];
        for (const [itemId, count] of Object.entries(this.inventory)) {
            const itemData = CONSTANTS.ITEMS[itemId.toUpperCase()];
            if (itemData) {
                items.push({
                    id: itemId,
                    data: itemData,
                    count: count
                });
            }
        }
        return items;
    }
}

// Create global instance
const gameState = new GameState();

// Make available globally for browser
if (typeof window !== 'undefined') {
    window.gameState = gameState;
}
