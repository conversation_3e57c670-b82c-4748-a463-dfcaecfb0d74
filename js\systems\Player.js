// ===== PLAYER SYSTEM =====
// Player movement, interactions, and state management

class Player {
    constructor() {
        // Player will use gameState for position and direction
        this.purchaseInProgress = false; // Flag to prevent multiple simultaneous purchases
    }
    
    // Movement methods
    moveForward() {
        if (gameState.inCombat) return;
        
        const dir = gameState.getCurrentDirectionVector();
        const newX = gameState.player.x + dir.x;
        const newY = gameState.player.y + dir.y;
        
        if (gameState.isValidPosition(newX, newY)) {
            gameState.setPlayerPosition(newX, newY);
            gameState.incrementSteps();

            // Mark cell as visited
            gameState.dungeon[newY][newX].visited = true;

            // Process poison damage during exploration
            if (window.characterSystem) {
                const poisonMessages = characterSystem.processPoisonDamageExploration();
                if (poisonMessages.length > 0) {
                    this.showPoisonDamageMessage(poisonMessages);
                }
            }

            // Check for trap pit
            if (gameState.dungeon[newY][newX].trapPit) {
                this.triggerTrapPit();
            }

            // Check for enemy encounter
            if (gameState.dungeon[newY][newX].enemy) {
                if (gameState.isSmokeScreenActive()) {
                    // SMOKE SCREEN active - avoid encounter but keep enemy
                    this.showSmokeScreenAvoidanceMessage();
                    // Enemy remains in the cell for future encounters
                } else {
                    // Normal encounter - start combat
                    this.startCombat();
                }
            }

            // Check for enemy respawn
            if (gameState.enemyRespawnTimer <= 0) {
                this.respawnEnemies();
                gameState.resetEnemyRespawnTimer();
            }
        }
    }
    
    moveBackward() {
        if (gameState.inCombat) return;
        
        // Get reverse direction vector
        const reverseDirection = (gameState.player.direction + 2) % 4;
        const dir = CONSTANTS.DIRECTION_VECTORS[reverseDirection];
        const newX = gameState.player.x + dir.x;
        const newY = gameState.player.y + dir.y;
        
        if (gameState.isValidPosition(newX, newY)) {
            gameState.setPlayerPosition(newX, newY);
            gameState.incrementSteps();

            // Mark cell as visited
            gameState.dungeon[newY][newX].visited = true;

            // Process poison damage during exploration
            if (window.characterSystem) {
                const poisonMessages = characterSystem.processPoisonDamageExploration();
                if (poisonMessages.length > 0) {
                    this.showPoisonDamageMessage(poisonMessages);
                }
            }

            // Check for trap pit
            if (gameState.dungeon[newY][newX].trapPit) {
                this.triggerTrapPit();
            }

            // Check for enemy encounter
            if (gameState.dungeon[newY][newX].enemy) {
                if (gameState.isSmokeScreenActive()) {
                    // SMOKE SCREEN active - avoid encounter but keep enemy
                    this.showSmokeScreenAvoidanceMessage();
                    // Enemy remains in the cell for future encounters
                } else {
                    // Normal encounter - start combat
                    this.startCombat();
                }
            }

            // Check for enemy respawn
            if (gameState.enemyRespawnTimer <= 0) {
                this.respawnEnemies();
                gameState.resetEnemyRespawnTimer();
            }
        }
    }
    
    turnLeft() {
        if (gameState.inCombat) return;
        const newDirection = (gameState.player.direction + 3) % 4;
        gameState.setPlayerDirection(newDirection);
    }
    
    turnRight() {
        if (gameState.inCombat) return;
        const newDirection = (gameState.player.direction + 1) % 4;
        gameState.setPlayerDirection(newDirection);
    }
    
    // Interaction methods
    openDoor() {
        const facingCell = gameState.getFacingCell();
        if (facingCell && facingCell.type === 'door' && !facingCell.opened) {
            // Check if this is a special door (3 sides surrounded by walls)
            if (this.isSpecialDoor(facingCell)) {
                this.openSpecialDoor(facingCell);
            } else {
                document.getElementById('doorUI').classList.add('active');
            }
        }
    }
    
    // Check if door qualifies as special door (3 sides surrounded by walls)
    isSpecialDoor(doorCell) {
        const facingPos = gameState.getFacingPosition();
        const playerDir = gameState.player.direction;
        
        // Get the three positions to check (left, right, and opposite from player)
        const leftDir = CONSTANTS.DIRECTION_VECTORS[(playerDir + 3) % 4];
        const rightDir = CONSTANTS.DIRECTION_VECTORS[(playerDir + 1) % 4];
        const oppositeDir = CONSTANTS.DIRECTION_VECTORS[playerDir];
        
        const leftPos = { x: facingPos.x + leftDir.x, y: facingPos.y + leftDir.y };
        const rightPos = { x: facingPos.x + rightDir.x, y: facingPos.y + rightDir.y };
        const oppositePos = { x: facingPos.x + oppositeDir.x, y: facingPos.y + oppositeDir.y };
        
        // Check if all three positions are walls or out of bounds
        const isLeftWall = this.isWallOrOutOfBounds(leftPos.x, leftPos.y);
        const isRightWall = this.isWallOrOutOfBounds(rightPos.x, rightPos.y);
        const isOppositeWall = this.isWallOrOutOfBounds(oppositePos.x, oppositePos.y);
        
        return isLeftWall && isRightWall && isOppositeWall;
    }
    
    // Helper method to check if position is wall or out of bounds
    isWallOrOutOfBounds(x, y) {
        if (x < 0 || x >= CONSTANTS.DUNGEON_SIZE || y < 0 || y >= CONSTANTS.DUNGEON_SIZE) {
            return true; // Out of bounds counts as wall
        }
        return gameState.dungeon[y][x].type === 'wall';
    }
    
    // Open special door and trigger shop event
    openSpecialDoor(doorCell) {
        // Play door opening sound effect
        if (window.soundManager) {
            soundManager.playDoorOpenSound();
        }

        // Use pre-assigned shop type from dungeon generation
        if (doorCell.shopType) {
            // Trigger shop UI
            this.showShopUI(doorCell.shopType);
        } else {
            // Fallback for doors without shops (regular doors) - open them normally
            const facingPos = gameState.getFacingPosition();

            // Open the door (keep it as door type but mark as opened)
            doorCell.opened = true;

            // Automatically move player into the opened door cell
            if (gameState.isValidPosition(facingPos.x, facingPos.y)) {
                gameState.setPlayerPosition(facingPos.x, facingPos.y);
                gameState.incrementSteps();

                // Mark cell as visited
                doorCell.visited = true;

                // Check for trap pit
                if (doorCell.trapPit) {
                    this.triggerTrapPit();
                }

                // Check for enemy encounter
                if (doorCell.enemy) {
                    this.startCombat();
                }

                // Check for enemy respawn
                if (gameState.enemyRespawnTimer <= 0) {
                    this.respawnEnemies();
                    gameState.resetEnemyRespawnTimer();
                }
            }

            gameState.dispatchEvent(CONSTANTS.EVENTS.GAME_STATE_CHANGED);
        }
    }
    
    // Show shop UI with the appropriate shop type
    showShopUI(shopType) {
        const shopUI = document.getElementById('shopUI');
        const shopImage = document.getElementById('shopImage');
        const shopName = document.getElementById('shopName');
        const shopItem = document.getElementById('shopItem');
        const shopCost = document.getElementById('shopCost');
        
        let shopData;
        switch (shopType) {
            case 'merchant':
                shopData = CONSTANTS.SHOPS.MERCHANT;
                break;
            case 'fighter':
                shopData = CONSTANTS.SHOPS.FIGHTER;
                break;
            case 'priest':
                shopData = CONSTANTS.SHOPS.PRIEST;
                break;
            case 'witch':
                shopData = CONSTANTS.SHOPS.WITCH;
                break;
            case 'alchemist':
                shopData = CONSTANTS.SHOPS.ALCHEMIST;
                break;
        }
        
        if (shopData) {
            // Handle shop image with ImageManager if available
            if (window.imageManager && imageManager.isImageAvailable(shopData.image)) {
                // Image is preloaded and available
                shopImage.src = shopData.image;
                shopImage.alt = shopData.name;
                shopImage.style.display = 'block';

                // Hide any existing fallback
                const existingFallback = shopImage.parentElement.querySelector('.shop-image-fallback');
                if (existingFallback) {
                    existingFallback.style.display = 'none';
                }
            } else {
                // Set up image with error handling
                shopImage.src = shopData.image;
                shopImage.alt = shopData.name;

                // Add error handling for missing images
                shopImage.onerror = function() {
                    this.style.display = 'none';
                    // Create fallback element if it doesn't exist
                    let fallback = this.parentElement.querySelector('.shop-image-fallback');
                    if (!fallback) {
                        fallback = document.createElement('div');
                        fallback.className = 'shop-image-fallback';
                        fallback.innerHTML = `
                            <div style="font-size: 64px; color: #4CAF50;">🏪</div>
                            <div style="font-size: 14px; color: #4CAF50; margin-top: 10px;">${shopData.name}</div>
                        `;
                        this.parentElement.appendChild(fallback);
                    }
                    fallback.style.display = 'flex';
                };

                // Reset image display and hide fallback when image loads successfully
                shopImage.onload = function() {
                    this.style.display = 'block';
                    const fallback = this.parentElement.querySelector('.shop-image-fallback');
                    if (fallback) {
                        fallback.style.display = 'none';
                    }
                };
            }

            shopName.textContent = shopData.name;
            shopItem.textContent = shopData.item;
            shopCost.textContent = shopData.cost;

            // Store current shop data for purchase processing
            gameState.currentShop = shopData;

            // Update gold display
            this.updateShopGoldDisplay();

            // Show appropriate menu based on shop type
            if (shopType === 'alchemist') {
                this.showAlchemistMenu();
            } else {
                this.showMainMenu();
            }

            shopUI.classList.add('active');
        }
    }
    
    // Show main shop menu
    showMainMenu() {
        document.getElementById('shopMainMenu').style.display = 'block';
        document.getElementById('shopStatMenu').style.display = 'none';
        document.getElementById('shopItemMenu').style.display = 'none';
        document.getElementById('shopRestMenu').style.display = 'none';
    }

    // Update shop gold display
    updateShopGoldDisplay() {
        const goldElement = document.getElementById('shopGoldAmount');
        if (goldElement) {
            goldElement.textContent = gameState.gold;
        }
    }

    // Show stat enhancement menu
    showStatEnhancement() {
        // Play button sound effect
        if (window.soundManager) {
            soundManager.playButtonSound();
        }

        document.getElementById('shopMainMenu').style.display = 'none';
        document.getElementById('shopStatMenu').style.display = 'block';
        document.getElementById('shopItemMenu').style.display = 'none';
        document.getElementById('shopRestMenu').style.display = 'none';
    }
    
    // Show item purchase menu
    showItemPurchase() {
        // Play button sound effect
        if (window.soundManager) {
            soundManager.playButtonSound();
        }

        document.getElementById('shopMainMenu').style.display = 'none';
        document.getElementById('shopStatMenu').style.display = 'none';
        document.getElementById('shopItemMenu').style.display = 'block';
        document.getElementById('shopRestMenu').style.display = 'none';
    }

    // Show rest service menu
    showRestService() {
        // Play button sound effect
        if (window.soundManager) {
            soundManager.playButtonSound();
        }

        document.getElementById('shopMainMenu').style.display = 'none';
        document.getElementById('shopStatMenu').style.display = 'none';
        document.getElementById('shopItemMenu').style.display = 'none';
        document.getElementById('shopRestMenu').style.display = 'block';

        // Update rest cost based on shop type
        this.updateRestCost();
    }

    // Show alchemist menu (skill acquisition only)
    showAlchemistMenu() {
        // Play button sound effect
        if (window.soundManager) {
            soundManager.playButtonSound();
        }

        document.getElementById('shopMainMenu').style.display = 'none';
        document.getElementById('shopStatMenu').style.display = 'none';
        document.getElementById('shopItemMenu').style.display = 'none';
        document.getElementById('shopRestMenu').style.display = 'none';
        document.getElementById('shopAlchemistMenu').style.display = 'block';

        // Update the skill acquisition interface
        this.updateAlchemistSkillList();
    }

    // Update the alchemist skill list display
    updateAlchemistSkillList() {
        const skillListElement = document.getElementById('alchemistSkillList');
        if (!skillListElement) return;

        skillListElement.innerHTML = '';

        // Get available enhancement skills
        const skills = Object.values(CONSTANTS.ENHANCEMENT_SKILLS);

        skills.forEach(skill => {
            const skillElement = document.createElement('div');
            skillElement.className = 'alchemist-skill-option';

            // Check if player has enough resources
            const hasOrbs = gameState.getItemCount('orb') >= skill.cost.orb;
            const hasMysticOrbs = gameState.getItemCount('mystic_orb') >= skill.cost.mystic_orb;
            const canAfford = hasOrbs && hasMysticOrbs;

            if (!canAfford) {
                skillElement.classList.add('disabled');
            }

            skillElement.innerHTML = `
                <div class="skill-name">${skill.name}</div>
                <div class="skill-description">${skill.description}</div>
                <div class="skill-cost">
                    Cost: ${skill.cost.orb} ORB${skill.cost.mystic_orb > 0 ? `, ${skill.cost.mystic_orb} MYSTIC ORB` : ''}
                </div>
            `;

            if (canAfford) {
                skillElement.onclick = () => this.purchaseEnhancementSkill(skill.id);
            }

            skillListElement.appendChild(skillElement);
        });
    }

    // Purchase an enhancement skill
    purchaseEnhancementSkill(skillId) {
        // Prevent multiple simultaneous purchases
        if (this.purchaseInProgress) {
            return;
        }
        this.purchaseInProgress = true;

        // Play button sound effect
        if (window.soundManager) {
            soundManager.playButtonSound();
        }

        const skillData = CONSTANTS.ENHANCEMENT_SKILLS[skillId.toUpperCase()];
        if (!skillData) {
            this.purchaseInProgress = false;
            return;
        }

        // Check if player has enough resources
        const hasOrbs = gameState.getItemCount('orb') >= skillData.cost.orb;
        const hasMysticOrbs = gameState.getItemCount('mystic_orb') >= skillData.cost.mystic_orb;

        if (!hasOrbs || !hasMysticOrbs) {
            this.showMessage('Insufficient resources!', '#ff6666');
            this.purchaseInProgress = false;
            return;
        }

        // Deduct resources with validation
        let orbRemovalSuccess = true;
        let mysticOrbRemovalSuccess = true;

        if (skillData.cost.orb > 0) {
            orbRemovalSuccess = gameState.removeItem('orb', skillData.cost.orb);
            if (!orbRemovalSuccess) {
                this.showMessage('Failed to deduct ORBs!', '#ff6666');
                this.purchaseInProgress = false;
                return;
            }
        }

        if (skillData.cost.mystic_orb > 0) {
            mysticOrbRemovalSuccess = gameState.removeItem('mystic_orb', skillData.cost.mystic_orb);
            if (!mysticOrbRemovalSuccess) {
                // Refund ORBs if MYSTIC ORB removal failed
                if (skillData.cost.orb > 0) {
                    gameState.addItem('orb', skillData.cost.orb);
                }
                this.showMessage('Failed to deduct MYSTIC ORBs!', '#ff6666');
                this.purchaseInProgress = false;
                return;
            }
        }

        // Add skill to owned skills only if resource deduction was successful
        gameState.addOwnedSkill(skillId);

        // Show success message
        this.showMessage(`Acquired ${skillData.name}!`, '#66ff66');

        // Update the skill list display
        this.updateAlchemistSkillList();

        // Update UI
        gameState.dispatchEvent(CONSTANTS.EVENTS.GAME_STATE_CHANGED);

        // Reset purchase flag
        this.purchaseInProgress = false;
    }

    // Show skill equipment interface
    showSkillEquipment() {
        // Play button sound effect
        if (window.soundManager) {
            soundManager.playButtonSound();
        }

        const skillEquipmentUI = document.getElementById('skillEquipmentUI');
        const characterSelection = document.getElementById('skillCharacterSelection');
        const equipmentPanel = document.getElementById('skillEquipmentPanel');

        // Show character selection first
        characterSelection.style.display = 'block';
        equipmentPanel.style.display = 'none';

        // Populate character list
        this.updateSkillCharacterList();

        skillEquipmentUI.style.display = 'block';
    }

    // Close skill equipment interface
    closeSkillEquipment() {
        // Play button sound effect
        if (window.soundManager) {
            soundManager.playButtonSound();
        }

        document.getElementById('skillEquipmentUI').style.display = 'none';
    }

    // Update character list for skill equipment
    updateSkillCharacterList() {
        const characterListElement = document.getElementById('skillCharacterList');
        if (!characterListElement) return;

        characterListElement.innerHTML = '';

        gameState.party.forEach(character => {
            if (character.life > 0) { // Only show living characters
                const characterElement = document.createElement('div');
                characterElement.className = 'character-option';
                characterElement.innerHTML = `
                    <div class="character-name">${character.name}</div>
                    <div class="character-class">${character.class}</div>
                    <div class="equipped-count">${gameState.getEquippedSkills(character.name).length}/4 skills equipped</div>
                `;
                characterElement.onclick = () => this.selectCharacterForSkillEquipment(character.name);
                characterListElement.appendChild(characterElement);
            }
        });
    }

    // Select character for skill equipment
    selectCharacterForSkillEquipment(characterName) {
        // Play button sound effect
        if (window.soundManager) {
            soundManager.playButtonSound();
        }

        const character = gameState.party.find(c => c.name === characterName);
        if (!character) return;

        // Hide character selection, show equipment panel
        document.getElementById('skillCharacterSelection').style.display = 'none';
        document.getElementById('skillEquipmentPanel').style.display = 'block';

        // Update selected character info
        document.getElementById('selectedCharacterName').textContent = characterName;

        // Update character stats display (with enhancements)
        const enhancedStats = gameState.getEnhancedStats(character);
        document.getElementById('charLife').textContent = enhancedStats.maxLife;
        document.getElementById('charStr').textContent = enhancedStats.str;
        document.getElementById('charInt').textContent = enhancedStats.int;
        document.getElementById('charAgl').textContent = enhancedStats.agl;

        // Update skill lists
        this.updateEquippedSkillsList(characterName);
        this.updateAvailableSkillsList(characterName);
    }

    // Back to character selection
    backToCharacterSelection() {
        // Play button sound effect
        if (window.soundManager) {
            soundManager.playButtonSound();
        }

        document.getElementById('skillCharacterSelection').style.display = 'block';
        document.getElementById('skillEquipmentPanel').style.display = 'none';

        // Refresh character list in case equipped skills changed
        this.updateSkillCharacterList();
    }

    // Update equipped skills list for character
    updateEquippedSkillsList(characterName) {
        const equippedListElement = document.getElementById('equippedSkillsList');
        if (!equippedListElement) return;

        equippedListElement.innerHTML = '';

        const equippedSkills = gameState.getEquippedSkills(characterName);

        if (equippedSkills.length === 0) {
            equippedListElement.innerHTML = '<div class="no-skills">No skills equipped</div>';
            return;
        }

        equippedSkills.forEach(skillId => {
            const skillData = CONSTANTS.ENHANCEMENT_SKILLS[skillId.toUpperCase()];
            if (skillData) {
                const skillElement = document.createElement('div');
                skillElement.className = 'equipped-skill';
                skillElement.innerHTML = `
                    <div class="skill-name">${skillData.name}</div>
                    <div class="skill-description">${skillData.description}</div>
                    <button class="unequip-btn" onclick="player.unequipSkill('${characterName}', '${skillId}')">Unequip</button>
                `;
                equippedListElement.appendChild(skillElement);
            }
        });
    }

    // Update available skills list for character
    updateAvailableSkillsList(characterName) {
        const availableListElement = document.getElementById('availableSkillsList');
        if (!availableListElement) return;

        availableListElement.innerHTML = '';

        const ownedSkills = gameState.getOwnedSkills();
        const equippedSkills = gameState.getEquippedSkills(characterName);
        const availableSkills = ownedSkills.filter(skillId => !equippedSkills.includes(skillId));

        if (availableSkills.length === 0) {
            availableListElement.innerHTML = '<div class="no-skills">No skills available</div>';
            return;
        }

        availableSkills.forEach(skillId => {
            const skillData = CONSTANTS.ENHANCEMENT_SKILLS[skillId.toUpperCase()];
            if (skillData) {
                const skillElement = document.createElement('div');
                skillElement.className = 'available-skill';

                // Check if character can equip more skills
                const canEquip = equippedSkills.length < 4;

                if (!canEquip) {
                    skillElement.classList.add('disabled');
                }

                skillElement.innerHTML = `
                    <div class="skill-name">${skillData.name}</div>
                    <div class="skill-description">${skillData.description}</div>
                    ${canEquip ? `<button class="equip-btn" onclick="player.equipSkill('${characterName}', '${skillId}')">Equip</button>` : '<div class="max-skills">Max skills equipped</div>'}
                `;
                availableListElement.appendChild(skillElement);
            }
        });
    }

    // Equip skill to character
    equipSkill(characterName, skillId) {
        // Play button sound effect
        if (window.soundManager) {
            soundManager.playButtonSound();
        }

        const success = gameState.equipSkillToCharacter(characterName, skillId);
        if (success) {
            // Update displays
            this.updateEquippedSkillsList(characterName);
            this.updateAvailableSkillsList(characterName);

            // Update character stats display
            const character = gameState.party.find(c => c.name === characterName);
            if (character) {
                const enhancedStats = gameState.getEnhancedStats(character);
                document.getElementById('charLife').textContent = enhancedStats.maxLife;
                document.getElementById('charStr').textContent = enhancedStats.str;
                document.getElementById('charInt').textContent = enhancedStats.int;
                document.getElementById('charAgl').textContent = enhancedStats.agl;
            }

            this.showMessage(`Equipped ${CONSTANTS.ENHANCEMENT_SKILLS[skillId.toUpperCase()].name}!`, '#66ff66');
        } else {
            this.showMessage('Cannot equip skill!', '#ff6666');
        }
    }

    // Unequip skill from character
    unequipSkill(characterName, skillId) {
        // Play button sound effect
        if (window.soundManager) {
            soundManager.playButtonSound();
        }

        const success = gameState.unequipSkillFromCharacter(characterName, skillId);
        if (success) {
            // Update displays
            this.updateEquippedSkillsList(characterName);
            this.updateAvailableSkillsList(characterName);

            // Update character stats display
            const character = gameState.party.find(c => c.name === characterName);
            if (character) {
                const enhancedStats = gameState.getEnhancedStats(character);
                document.getElementById('charLife').textContent = enhancedStats.maxLife;
                document.getElementById('charStr').textContent = enhancedStats.str;
                document.getElementById('charInt').textContent = enhancedStats.int;
                document.getElementById('charAgl').textContent = enhancedStats.agl;
            }

            this.showMessage(`Unequipped ${CONSTANTS.ENHANCEMENT_SKILLS[skillId.toUpperCase()].name}!`, '#ffff66');
        } else {
            this.showMessage('Cannot unequip skill!', '#ff6666');
        }
    }

    // Update rest cost display based on current shop type
    updateRestCost() {
        const restCostElement = document.getElementById('restCost');
        if (!restCostElement || !gameState.currentShop) return;

        let cost;
        if (gameState.currentShop.type === 'priest') {
            cost = CONSTANTS.REST_COSTS.CHAPEL;
        } else {
            cost = CONSTANTS.REST_COSTS.DEFAULT;
        }

        restCostElement.textContent = cost;
    }

    // Purchase rest service
    restService() {
        // Play button sound effect
        if (window.soundManager) {
            soundManager.playButtonSound();
        }

        if (!gameState.currentShop) return;

        // Determine cost based on shop type
        let cost;
        if (gameState.currentShop.type === 'priest') {
            cost = CONSTANTS.REST_COSTS.CHAPEL;
        } else {
            cost = CONSTANTS.REST_COSTS.DEFAULT;
        }

        // Check if player has enough gold
        if (gameState.gold < cost) {
            this.showInsufficientGoldForRestMessage(cost);
            return;
        }

        // Deduct gold
        gameState.gold -= cost;

        // Update shop gold display
        this.updateShopGoldDisplay();

        // Heal all living party members
        gameState.party.forEach(member => {
            if (member.life > 0) {
                member.life = member.maxLife;
            }
        });

        // Show success message
        this.showRestSuccessMessage(cost);

        // Close shop UI
        this.closeShopUI();

        // Update UI
        gameState.dispatchEvent(CONSTANTS.EVENTS.GAME_STATE_CHANGED);
    }

    // Go back to main menu
    backToMainMenu() {
        // Play button sound effect
        if (window.soundManager) {
            soundManager.playButtonSound();
        }

        this.showMainMenu();
    }
    
    // Purchase an item directly
    purchaseItem(itemId) {
        // Play button sound effect
        if (window.soundManager) {
            soundManager.playButtonSound();
        }

        const itemData = CONSTANTS.ITEMS[itemId.toUpperCase()];
        if (!itemData) {
            console.error(`Unknown item: ${itemId}`);
            return;
        }

        // Special check for mapping tool - don't allow purchase if already obtained
        if (itemId.toLowerCase() === 'mapping_tool' && gameState.hasMappingTool) {
            this.showMessage('You already have the Mapping Tool!', '#ffff66');
            return;
        }

        // Check if player has enough gold
        if (gameState.gold < itemData.shopPrice) {
            this.showInsufficientGoldMessage();
            return;
        }

        // Check if inventory can hold the item (skip for mapping tool as it's not stored in inventory)
        if (itemId.toLowerCase() !== 'mapping_tool' && !gameState.canAddItem(itemId, 1)) {
            this.showInventoryFullMessage();
            return;
        }

        // Deduct gold
        gameState.gold -= itemData.shopPrice;

        // Update shop gold display
        this.updateShopGoldDisplay();

        // Special handling for mapping tool
        if (itemId.toLowerCase() === 'mapping_tool') {
            // Set the mapping tool flag and trigger notification (don't add to inventory)
            gameState.obtainMappingTool();
        } else {
            // Add regular items to inventory
            gameState.addItem(itemId, 1);
        }

        // Show success message
        this.showItemPurchaseSuccessMessage(itemData);

        // Update UI
        gameState.dispatchEvent(CONSTANTS.EVENTS.GAME_STATE_CHANGED);
    }
    
    // Show inventory full message
    showInventoryFullMessage() {
        const messageContainer = document.createElement('div');
        messageContainer.className = 'inventory-full-message';
        messageContainer.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.9);
            color: #ff6b6b;
            padding: 20px;
            border: 2px solid #ff6b6b;
            border-radius: 8px;
            font-family: 'IM Fell English SC', serif;
            font-size: 16px;
            text-align: center;
            z-index: 1000;
            max-width: 300px;
        `;
        
        messageContainer.innerHTML = `
            <div style="color: #ffff66; font-weight: bold; margin-bottom: 10px;">Inventory Full!</div>
            <div>Your inventory is full. Please use or discard some items first.</div>
            <div style="margin-top: 15px; font-size: 12px; color: #ccc;">Click to continue...</div>
        `;
        
        // Add click handler to remove message
        messageContainer.addEventListener('click', () => {
            document.body.removeChild(messageContainer);
        });
        
        // Auto-remove after 3 seconds
        setTimeout(() => {
            if (messageContainer.parentNode) {
                document.body.removeChild(messageContainer);
            }
        }, 3000);
        
        document.body.appendChild(messageContainer);
    }
    
    // Show item purchase success message
    showItemPurchaseSuccessMessage(itemData) {
        const messageContainer = document.createElement('div');
        messageContainer.className = 'item-purchase-success-message';
        messageContainer.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.9);
            color: #66ff66;
            padding: 20px;
            border: 2px solid #66ff66;
            border-radius: 8px;
            font-family: 'IM Fell English SC', serif;
            font-size: 16px;
            text-align: center;
            z-index: 1000;
            max-width: 350px;
        `;
        
        messageContainer.innerHTML = `
            <div style="color: #ffff66; font-weight: bold; margin-bottom: 10px;">Item Purchased!</div>
            <div>${itemData.name} has been added to your inventory!</div>
            <div style="margin-top: 15px; font-size: 12px; color: #ccc;">Click to continue...</div>
        `;
        
        // Add click handler to remove message
        messageContainer.addEventListener('click', () => {
            document.body.removeChild(messageContainer);
        });
        
        // Auto-remove after 4 seconds
        setTimeout(() => {
            if (messageContainer.parentNode) {
                document.body.removeChild(messageContainer);
            }
        }, 4000);
        
        document.body.appendChild(messageContainer);
    }

    // Generic message display method
    showMessage(message, color = '#ffff66') {
        const messageContainer = document.createElement('div');
        messageContainer.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.9);
            border: 2px solid ${color};
            padding: 20px;
            border-radius: 10px;
            color: ${color};
            font-family: 'IM Fell English SC', serif;
            font-size: 16px;
            z-index: 1000;
            text-align: center;
            cursor: pointer;
            box-shadow: 0 0 20px rgba(255, 255, 102, 0.5);
        `;

        messageContainer.innerHTML = `
            <div style="margin-bottom: 10px;">${message}</div>
            <div style="margin-top: 15px; font-size: 12px; color: #ccc;">Click to continue...</div>
        `;

        // Add click handler to remove message
        messageContainer.addEventListener('click', () => {
            document.body.removeChild(messageContainer);
        });

        // Auto-remove after 4 seconds
        setTimeout(() => {
            if (messageContainer.parentNode) {
                document.body.removeChild(messageContainer);
            }
        }, 4000);

        document.body.appendChild(messageContainer);
    }

    confirmOpenDoor() {
        const facingCell = gameState.getFacingCell();
        const facingPos = gameState.getFacingPosition();
        
        if (facingCell && facingCell.type === 'door') {
            // Play door opening sound effect
            if (window.soundManager) {
                soundManager.playDoorOpenSound();
            }

            // Open the door (keep it as door type but mark as opened)
            facingCell.opened = true;

            // Close the door UI
            this.closeDoorUI();

            // Automatically move player into the opened door cell
            if (gameState.isValidPosition(facingPos.x, facingPos.y)) {
                gameState.setPlayerPosition(facingPos.x, facingPos.y);
                gameState.incrementSteps();

                // Mark cell as visited
                facingCell.visited = true;

                // Check for trap pit
                if (facingCell.trapPit) {
                    this.triggerTrapPit();
                }

                // Check for enemy encounter
                if (facingCell.enemy) {
                    this.startCombat();
                }

                // Check for enemy respawn
                if (gameState.enemyRespawnTimer <= 0) {
                    this.respawnEnemies();
                    gameState.resetEnemyRespawnTimer();
                }
            }
            
            gameState.dispatchEvent(CONSTANTS.EVENTS.GAME_STATE_CHANGED);
        }
    }
    
    closeDoorUI() {
        document.getElementById('doorUI').classList.remove('active');
    }
    
    // Shop system methods
    closeShopUI() {
        // Play button sound effect
        if (window.soundManager) {
            soundManager.playButtonSound();
        }

        document.getElementById('shopUI').classList.remove('active');
        gameState.currentShop = null;
    }

    initiatePurchase() {
        // Play button sound effect
        if (window.soundManager) {
            soundManager.playButtonSound();
        }

        if (!gameState.currentShop) return;

        // Check if player has enough gold
        if (gameState.gold < gameState.currentShop.cost) {
            this.showInsufficientGoldMessage();
            return;
        }

        // Show party selection UI
        this.showPartySelection();
    }
    
    showInsufficientGoldMessage() {
        // Create a temporary message display
        const messageContainer = document.createElement('div');
        messageContainer.className = 'insufficient-gold-message';
        messageContainer.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.9);
            color: #ff6b6b;
            padding: 20px;
            border: 2px solid #ff6b6b;
            border-radius: 8px;
            font-family: 'IM Fell English SC', serif;
            font-size: 16px;
            text-align: center;
            z-index: 1000;
            max-width: 300px;
        `;
        
        messageContainer.innerHTML = `
            <div style="color: #ffff66; font-weight: bold; margin-bottom: 10px;">Insufficient Gold!</div>
            <div>You need ${gameState.currentShop.cost} gold to purchase this item.</div>
            <div style="margin-top: 15px; font-size: 12px; color: #ccc;">Click to continue...</div>
        `;
        
        // Add click handler to remove message
        messageContainer.addEventListener('click', () => {
            document.body.removeChild(messageContainer);
        });
        
        // Auto-remove after 3 seconds
        setTimeout(() => {
            if (messageContainer.parentNode) {
                document.body.removeChild(messageContainer);
            }
        }, 3000);
        
        document.body.appendChild(messageContainer);
    }

    showInsufficientGoldForRestMessage(cost) {
        // Create a temporary message display
        const messageContainer = document.createElement('div');
        messageContainer.className = 'insufficient-gold-message';
        messageContainer.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.9);
            color: #ff6b6b;
            padding: 20px;
            border: 2px solid #ff6b6b;
            border-radius: 8px;
            font-family: 'IM Fell English SC', serif;
            font-size: 16px;
            text-align: center;
            z-index: 1000;
            max-width: 300px;
        `;

        messageContainer.innerHTML = `
            <div style="color: #ffff66; font-weight: bold; margin-bottom: 10px;">Insufficient Gold!</div>
            <div>You need ${cost} gold for rest services.</div>
            <div style="margin-top: 15px; font-size: 12px; color: #ccc;">Click to continue...</div>
        `;

        // Add click handler to remove message
        messageContainer.addEventListener('click', () => {
            document.body.removeChild(messageContainer);
        });

        // Auto-remove after 3 seconds
        setTimeout(() => {
            if (messageContainer.parentNode) {
                document.body.removeChild(messageContainer);
            }
        }, 3000);

        document.body.appendChild(messageContainer);
    }

    showPartySelection() {
        const partySelectionUI = document.getElementById('partySelectionUI');
        const partySelectionMembers = document.getElementById('partySelectionMembers');
        const selectionDescription = document.getElementById('selectionDescription');

        // Update description based on shop type
        const effectDescription = this.getEffectDescription(gameState.currentShop.effect);
        selectionDescription.textContent = `Select who should receive ${effectDescription}:`;

        // Clear previous buttons
        partySelectionMembers.innerHTML = '';

        // Check if this is Chapel (priest shop) for special handling
        const isChapel = gameState.currentShop.type === 'priest';

        // Create buttons for each party member
        gameState.party.forEach((member, index) => {
            const button = document.createElement('button');
            const isDead = member.life <= 0;

            button.className = 'party-member-btn' + (isDead ? ' dead' : '');

            // For Chapel: only enable dead characters
            // For other shops: only enable alive characters
            if (isChapel) {
                button.disabled = !isDead; // Enable only dead characters
            } else {
                button.disabled = isDead;  // Enable only alive characters
            }

            button.onclick = () => this.confirmPurchase(index);

            button.innerHTML = `
                <div class="party-member-name">${member.name} (Level ${member.level})</div>
                <div class="party-member-stats">
                    LIFE: ${member.life}/${member.maxLife} | STR: ${member.str} | INT: ${member.int} | AGL: ${member.agl}
                </div>
            `;

            partySelectionMembers.appendChild(button);
        });

        partySelectionUI.classList.add('active');
    }
    
    getEffectDescription(effect) {
        switch (effect) {
            case 'maxLife':
                return '+5 Max Life';
            case 'str':
                return '+1 Strength';
            case 'int':
                return '+1 Intelligence';
            case 'life':
                // Check if this is Chapel for special description
                if (gameState.currentShop && gameState.currentShop.type === 'priest') {
                    return 'Revival (Fallen characters only)';
                } else {
                    return 'Full Healing';
                }
            default:
                return 'an enhancement';
        }
    }
    
    confirmPurchase(memberIndex) {
        // Play button sound effect
        if (window.soundManager) {
            soundManager.playButtonSound();
        }

        if (!gameState.currentShop || memberIndex < 0 || memberIndex >= gameState.party.length) {
            return;
        }

        const member = gameState.party[memberIndex];
        const shop = gameState.currentShop;

        // Deduct gold
        gameState.gold -= shop.cost;

        // Update shop gold display
        this.updateShopGoldDisplay();

        // Apply effect to selected member
        this.applyShopEffect(member, shop.effect, shop.value);

        // Show success message
        this.showPurchaseSuccessMessage(member, shop);

        // Close UIs
        this.cancelPartySelection();
        this.closeShopUI();

        // Update UI
        gameState.dispatchEvent(CONSTANTS.EVENTS.GAME_STATE_CHANGED);
    }
    
    applyShopEffect(member, effect, value) {
        switch (effect) {
            case 'maxLife':
                member.maxLife += value;
                member.life += value; // Also heal by the same amount
                break;
            case 'str':
                member.str += value;
                break;
            case 'int':
                member.int += value;
                break;
            case 'life':
                // Chapel effect: Revive fallen characters or heal living ones
                if (member.life <= 0) {
                    // Revive with full health
                    member.life = member.maxLife;
                } else {
                    // Full healing for living characters (though this shouldn't happen with new Chapel logic)
                    member.life = member.maxLife;
                }
                break;
        }
    }
    
    showPurchaseSuccessMessage(member, shop) {
        const messageContainer = document.createElement('div');
        messageContainer.className = 'purchase-success-message';
        messageContainer.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.9);
            color: #66ff66;
            padding: 20px;
            border: 2px solid #66ff66;
            border-radius: 8px;
            font-family: 'IM Fell English SC', serif;
            font-size: 16px;
            text-align: center;
            z-index: 1000;
            max-width: 350px;
        `;
        
        let effectText;
        if (shop.type === 'priest' && shop.effect === 'life') {
            // Special message for Chapel revival
            effectText = `${member.name} has been revived with full health!`;
        } else {
            effectText = `${member.name} received ${this.getEffectDescription(shop.effect)}!`;
        }

        messageContainer.innerHTML = `
            <div style="color: #ffff66; font-weight: bold; margin-bottom: 10px;">Purchase Successful!</div>
            <div>${effectText}</div>
            <div style="margin-top: 15px; font-size: 12px; color: #ccc;">Click to continue...</div>
        `;
        
        // Add click handler to remove message
        messageContainer.addEventListener('click', () => {
            document.body.removeChild(messageContainer);
        });
        
        // Auto-remove after 4 seconds
        setTimeout(() => {
            if (messageContainer.parentNode) {
                document.body.removeChild(messageContainer);
            }
        }, 4000);
        
        document.body.appendChild(messageContainer);
    }

    showRestSuccessMessage(cost) {
        const messageContainer = document.createElement('div');
        messageContainer.className = 'rest-success-message';
        messageContainer.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.9);
            color: #66ff66;
            padding: 20px;
            border: 2px solid #66ff66;
            border-radius: 8px;
            font-family: 'IM Fell English SC', serif;
            font-size: 16px;
            text-align: center;
            z-index: 1000;
            max-width: 350px;
        `;

        messageContainer.innerHTML = `
            <div style="color: #ffff66; font-weight: bold; margin-bottom: 10px;">Rest Complete!</div>
            <div>All living party members have been fully healed!</div>
            <div style="margin-top: 10px; font-size: 14px; color: #ccc;">Cost: ${cost} Gold</div>
            <div style="margin-top: 15px; font-size: 12px; color: #ccc;">Click to continue...</div>
        `;

        // Add click handler to remove message
        messageContainer.addEventListener('click', () => {
            document.body.removeChild(messageContainer);
        });

        // Auto-remove after 4 seconds
        setTimeout(() => {
            if (messageContainer.parentNode) {
                document.body.removeChild(messageContainer);
            }
        }, 4000);

        document.body.appendChild(messageContainer);
    }

    cancelPartySelection() {
        // Play button sound effect
        if (window.soundManager) {
            soundManager.playButtonSound();
        }

        document.getElementById('partySelectionUI').classList.remove('active');
    }
    
    // Item system methods
    useItem() {
        if (gameState.inCombat) return; // Cannot use items during combat
        
        const items = gameState.getInventoryItems();
        if (items.length === 0) {
            this.showNoItemsMessage();
            return;
        }
        
        this.showItemUI(items);
    }
    
    showItemUI(items) {
        const itemUI = document.getElementById('itemUI');
        const itemList = document.getElementById('itemList');
        
        // Clear previous items
        itemList.innerHTML = '';
        
        // Create buttons for each item
        items.forEach(item => {
            const button = document.createElement('button');
            button.className = 'item-btn';
            button.onclick = () => this.selectItem(item.id);
            
            button.innerHTML = `
                <div class="item-name">${item.data.name}</div>
                <div class="item-description">${item.data.description}</div>
                <div class="item-count">x${item.count}</div>
            `;
            
            itemList.appendChild(button);
        });
        
        itemUI.classList.add('active');
    }
    
    selectItem(itemId) {
        // Close item UI
        this.closeItemUI();
        
        // Store selected item for use
        gameState.selectedItem = itemId;
        
        const itemData = CONSTANTS.ITEMS[itemId.toUpperCase()];
        
        // Check if this is a party-wide effect (like smoke screen)
        if (itemData && itemData.effect === 'stealth') {
            // Skip party selection for party-wide effects
            this.confirmItemUse(0, itemId); // Use index 0 but effect applies to whole party
        } else {
            // Show party selection for targeting
            this.showPartySelectionForItem(itemId);
        }
    }
    
    showPartySelectionForItem(itemId) {
        const itemData = CONSTANTS.ITEMS[itemId.toUpperCase()];
        const partySelectionUI = document.getElementById('partySelectionUI');
        const partySelectionMembers = document.getElementById('partySelectionMembers');
        const selectionDescription = document.getElementById('selectionDescription');
        
        // Update description
        selectionDescription.textContent = `Select who should use ${itemData.name}:`;
        
        // Clear previous buttons
        partySelectionMembers.innerHTML = '';
        
        // Create buttons for each party member
        gameState.party.forEach((member, index) => {
            const button = document.createElement('button');
            button.className = 'party-member-btn' + (member.life <= 0 ? ' dead' : '');
            button.disabled = member.life <= 0;
            button.onclick = () => this.confirmItemUse(index, itemId);
            
            button.innerHTML = `
                <div class="party-member-name">${member.name} (Level ${member.level})</div>
                <div class="party-member-stats">
                    LIFE: ${member.life}/${member.maxLife} | STR: ${member.str} | INT: ${member.int} | AGL: ${member.agl}
                </div>
            `;
            
            partySelectionMembers.appendChild(button);
        });
        
        partySelectionUI.classList.add('active');
    }
    
    confirmItemUse(memberIndex, itemId) {
        const member = gameState.party[memberIndex];
        const itemData = CONSTANTS.ITEMS[itemId.toUpperCase()];

        if (!member || !itemData) {
            console.error('Invalid member or item for item use');
            return;
        }

        // Check if we have the item
        if (gameState.getItemCount(itemId) <= 0) {
            this.showNoItemsMessage();
            return;
        }

        // Apply item effect and check if item was consumed
        const itemConsumed = this.applyItemEffect(member, itemData);

        if (itemConsumed) {
            // Remove item from inventory only if it was consumed
            gameState.removeItem(itemId, 1);

            // Show success message
            this.showItemUseSuccessMessage(member, itemData);
        }

        // Close UI
        this.cancelPartySelection();

        // Clear selected item
        gameState.selectedItem = null;

        // Update UI
        gameState.dispatchEvent(CONSTANTS.EVENTS.GAME_STATE_CHANGED);
    }
    
    applyItemEffect(member, itemData) {
        switch (itemData.effect) {
            case 'heal':
                const healAmount = Math.floor(member.maxLife * itemData.value);
                member.life = Math.min(member.maxLife, member.life + healAmount);
                break;
            case 'stealth':
                // Smoke screen affects the whole party, not individual members
                gameState.activateSmokeScreen(itemData.value);
                break;
            case 'cure_poison':
                // Cure poison from the selected member
                if (window.characterSystem) {
                    characterSystem.curePoison(member);
                }
                break;
            case 'mapping':
                // Activate mapping tool functionality
                gameState.obtainMappingTool();
                break;
            case 'no_effect':
                // ORB and other passive items cannot be used
                this.showMessage('This item cannot be used directly. It is a passive collectible.', '#ff6666');
                return false; // Indicate that the item was not consumed
            // Add more item effects here as needed
            default:
                console.warn(`Unknown item effect: ${itemData.effect}`);
        }
        return true; // Indicate that the item was consumed
    }
    
    showItemUseSuccessMessage(member, itemData) {
        const messageContainer = document.createElement('div');
        messageContainer.className = 'item-use-success-message';
        messageContainer.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.9);
            color: #66ff66;
            padding: 20px;
            border: 2px solid #66ff66;
            border-radius: 8px;
            font-family: 'IM Fell English SC', serif;
            font-size: 16px;
            text-align: center;
            z-index: 1000;
            max-width: 350px;
        `;
        
        let effectText = '';
        if (itemData.effect === 'heal') {
            const healAmount = Math.floor(member.maxLife * itemData.value);
            effectText = `${member.name} recovered ${healAmount} life!`;
        } else if (itemData.effect === 'stealth') {
            effectText = `Smoke screen activated! Your party will avoid enemy encounters for ${itemData.value} steps.`;
        } else if (itemData.effect === 'cure_poison') {
            if (member.poisoned) {
                effectText = `${member.name}'s poison has been cured!`;
            } else {
                effectText = `${member.name} was not poisoned.`;
            }
        } else if (itemData.effect === 'mapping') {
            effectText = `Mapping Tool activated! The dungeon map is now available.`;
        }
        
        messageContainer.innerHTML = `
            <div style="color: #ffff66; font-weight: bold; margin-bottom: 10px;">Item Used!</div>
            <div>${effectText}</div>
            <div style="margin-top: 15px; font-size: 12px; color: #ccc;">Click to continue...</div>
        `;
        
        // Add click handler to remove message
        messageContainer.addEventListener('click', () => {
            document.body.removeChild(messageContainer);
        });
        
        // Auto-remove after 4 seconds
        setTimeout(() => {
            if (messageContainer.parentNode) {
                document.body.removeChild(messageContainer);
            }
        }, 4000);
        
        document.body.appendChild(messageContainer);
    }
    
    // Show smoke screen avoidance message
    showSmokeScreenAvoidanceMessage() {
        const messageContainer = document.createElement('div');
        messageContainer.className = 'smoke-screen-avoidance-message';
        messageContainer.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.9);
            color: #9966ff;
            padding: 20px;
            border: 2px solid #9966ff;
            border-radius: 8px;
            font-family: 'IM Fell English SC', serif;
            font-size: 16px;
            text-align: center;
            z-index: 1000;
            max-width: 350px;
        `;
        
        messageContainer.innerHTML = `
            <div style="color: #ffff66; font-weight: bold; margin-bottom: 10px;">💨 Smoke Screen Active! 💨</div>
            <div>The dense smoke conceals your party from the enemy!</div>
            <div style="margin-top: 10px; font-size: 14px;">Remaining steps: ${gameState.smokeScreenStepsRemaining}</div>
            <div style="margin-top: 15px; font-size: 12px; color: #ccc;">Click to continue...</div>
        `;
        
        // Add click handler to remove message
        messageContainer.addEventListener('click', () => {
            document.body.removeChild(messageContainer);
        });
        
        // Auto-remove after 3 seconds
        setTimeout(() => {
            if (messageContainer.parentNode) {
                document.body.removeChild(messageContainer);
            }
        }, 3000);
        
        document.body.appendChild(messageContainer);
    }
    
    showNoItemsMessage() {
        const messageContainer = document.createElement('div');
        messageContainer.className = 'no-items-message';
        messageContainer.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.9);
            color: #ff6b6b;
            padding: 20px;
            border: 2px solid #ff6b6b;
            border-radius: 8px;
            font-family: 'IM Fell English SC', serif;
            font-size: 16px;
            text-align: center;
            z-index: 1000;
            max-width: 300px;
        `;
        
        messageContainer.innerHTML = `
            <div style="color: #ffff66; font-weight: bold; margin-bottom: 10px;">No Items!</div>
            <div>You don't have any items to use.</div>
            <div style="margin-top: 15px; font-size: 12px; color: #ccc;">Click to continue...</div>
        `;
        
        // Add click handler to remove message
        messageContainer.addEventListener('click', () => {
            document.body.removeChild(messageContainer);
        });
        
        // Auto-remove after 3 seconds
        setTimeout(() => {
            if (messageContainer.parentNode) {
                document.body.removeChild(messageContainer);
            }
        }, 3000);
        
        document.body.appendChild(messageContainer);
    }
    
    closeItemUI() {
        document.getElementById('itemUI').classList.remove('active');
    }

    // Show poison damage message during exploration
    showPoisonDamageMessage(poisonMessages) {
        // Poison damage messages are suppressed - damage still applies but no visual notification
        return;
    }
    
    useHealPoint() {
        const currentCell = gameState.getCurrentCell();
        if (currentCell.type === 'heal') {
            // Play healing point sound effect
            if (window.soundManager) {
                soundManager.playHealingPointSound();
            }

            // Heal all party members
            gameState.party.forEach(member => {
                member.life = member.maxLife;
            });

            // Remove healing point
            currentCell.type = 'floor';

            // Show healing point notification overlay
            if (window.healingPointNotification) {
                window.healingPointNotification.showNotification();
            }

            gameState.dispatchEvent(CONSTANTS.EVENTS.GAME_STATE_CHANGED);
        }
    }
    
    descendStairs() {
        const currentCell = gameState.getCurrentCell();
        if (currentCell.type === 'stairs') {
            const nextFloor = gameState.currentFloor + 1;

            if (nextFloor > CONSTANTS.MAX_FLOORS) {
                document.getElementById('victoryScreen').classList.add('active');
                return;
            }

            // Reset respawn count for new floor
            gameState.enemyRespawnCount = 0;

            // Move to next floor
            gameState.setCurrentFloor(nextFloor);
            gameState.setPlayerPosition(CONSTANTS.PLAYER_START_X, CONSTANTS.PLAYER_START_Y);
            gameState.setDungeon(gameState.floors[nextFloor - 1]);
            gameState.dungeon[gameState.player.y][gameState.player.x].visited = true;

            // Show floor progression notification
            if (typeof floorProgressionNotification !== 'undefined') {
                floorProgressionNotification.showNotification(nextFloor);
            }
        }
    }
    
    // Trap pit system
    triggerTrapPit() {
        // Play pitfall trap sound effect
        if (window.soundManager) {
            soundManager.playPitfallSound();
        }

        const damageMessages = [];

        // Apply damage to random party members
        gameState.party.forEach(member => {
            // Random chance for each member to take damage
            if (Math.random() < 0.8) { // 80% chance each member takes damage
                // Calculate damage (10-20% of max life)
                const damagePercent = CONSTANTS.TRAP_PIT.DAMAGE_MIN +
                    Math.random() * (CONSTANTS.TRAP_PIT.DAMAGE_MAX - CONSTANTS.TRAP_PIT.DAMAGE_MIN);
                const damage = Math.floor(member.maxLife * damagePercent);

                // Apply damage but don't kill (minimum 1 life)
                member.life = Math.max(1, member.life - damage);

                damageMessages.push(`${member.name} fell into a trap pit and took ${damage} damage!`);
            }
        });
        
        // Show trap pit message if any damage was dealt
        if (damageMessages.length > 0) {
            this.showTrapPitMessage(damageMessages);
        }
        
        // Update UI to reflect damage
        gameState.dispatchEvent(CONSTANTS.EVENTS.GAME_STATE_CHANGED);
    }
    
    // Show trap pit damage message using the new notification system
    showTrapPitMessage(messages) {
        // Use the new trap pit notification system
        if (window.trapPitNotification) {
            trapPitNotification.showNotification(messages);
        } else {
            console.error('Trap pit notification system not available');
            // Fallback to console logging if notification system fails
            console.log('Trap pit damage:', messages.join(', '));
        }
    }
    
    // Combat initiation
    startCombat() {
        gameState.setCombatState(true);
        
        const currentCell = gameState.getCurrentCell();
        let enemies = [];
        
        // Check if this is a respawned enemy for stat multiplier
        const isRespawnedEnemy = currentCell.enemyRespawned;
        const statMultiplier = isRespawnedEnemy ? 
            1 + (gameState.enemyRespawnCount * CONSTANTS.ENEMY.RESPAWN_STAT_MULTIPLIER) : 1;
        
        if (currentCell.type === 'boss') {
            enemies = this.generateFinalBoss();
        } else {
            // Generate first enemy
            const firstEnemy = this.generateEnemy(statMultiplier);
            enemies.push(firstEnemy);

            // Check if the first enemy has solo spawn property
            const firstEnemyTemplate = this.getEnemyTemplate(firstEnemy.type);
            const isSoloSpawn = firstEnemyTemplate && firstEnemyTemplate.soloSpawn;

            // 15% chance to spawn a second enemy (only if first enemy is not solo spawn)
            if (!isSoloSpawn && Math.random() < CONSTANTS.MULTI_ENEMY.SPAWN_CHANCE) {
                enemies.push(this.generateSecondEnemy(statMultiplier));
            }
        }
        
        // Start combat system
        if (window.combatSystem) {
            combatSystem.startBattle(enemies);
        }
        
        document.getElementById('combatUI').classList.add('active');
    }
    
    // Helper function to get enemy template by name
    getEnemyTemplate(enemyName) {
        return Object.values(CONSTANTS.MONSTER_TYPES).find(template => template.name === enemyName);
    }

    // Enemy generation
    generateEnemy(statMultiplier = 1) {
        // Filter monster types based on current floor
        const availableMonsterTypes = Object.values(CONSTANTS.MONSTER_TYPES).filter(monsterType => {
            // Check minFloor requirement
            if (monsterType.minFloor && gameState.currentFloor < monsterType.minFloor) {
                return false;
            }
            // Check maxFloor requirement
            if (monsterType.maxFloor && gameState.currentFloor > monsterType.maxFloor) {
                return false;
            }
            return true;
        });

        // Select random monster type from available types
        const monsterType = availableMonsterTypes[Math.floor(Math.random() * availableMonsterTypes.length)];
        
        // Calculate base stats with floor scaling
        const floorMultiplier = 1 + (gameState.currentFloor - 1) * 0.3;
        const baseStats = {
            life: Math.floor(monsterType.baseStats.life * floorMultiplier),
            str: Math.floor(monsterType.baseStats.str * floorMultiplier),
            int: Math.floor(monsterType.baseStats.int * floorMultiplier),
            agl: Math.floor(monsterType.baseStats.agl * floorMultiplier)
        };
        
        const variance = 0.25;
        const enemy = {
            name: statMultiplier > 1 ?
                `Enhanced ${monsterType.name}` :
                monsterType.name,
            type: monsterType.name,
            imagePath: monsterType.imagePath,
            properties: monsterType.properties || [], // Include properties for spiritual_body detection
            behaviorPattern: monsterType.behaviorPattern,
            specialAbilities: monsterType.specialAbilities, // Include special abilities
            maxLife: Math.floor(baseStats.life * (1 + (Math.random() - 0.5) * variance) * statMultiplier),
            str: Math.floor(baseStats.str * (1 + (Math.random() - 0.5) * variance) * statMultiplier),
            int: Math.floor(baseStats.int * (1 + (Math.random() - 0.5) * variance) * statMultiplier),
            agl: Math.floor(baseStats.agl * (1 + (Math.random() - 0.5) * variance) * statMultiplier),
            hasRevived: false // Track if zombie has already revived
        };
        
        // Add random AGL bonus of 0-3 points
        enemy.agl += Math.floor(Math.random() * 4);
        
        enemy.life = enemy.maxLife;
        enemy.expReward = Math.floor((enemy.maxLife + enemy.str + enemy.int + enemy.agl) * gameState.currentFloor * 0.5);
        
        return enemy;
    }
    
    // Generate second enemy with different stats
    generateSecondEnemy(statMultiplier = 1) {
        // Filter monster types based on current floor and exclude solo spawn enemies
        const availableMonsterTypes = Object.values(CONSTANTS.MONSTER_TYPES).filter(monsterType => {
            // Check minFloor requirement
            if (monsterType.minFloor && gameState.currentFloor < monsterType.minFloor) {
                return false;
            }
            // Check maxFloor requirement
            if (monsterType.maxFloor && gameState.currentFloor > monsterType.maxFloor) {
                return false;
            }
            // Exclude solo spawn enemies from being selected as second enemy
            if (monsterType.soloSpawn) {
                return false;
            }
            return true;
        });

        // Select random monster type from available types
        const monsterType = availableMonsterTypes[Math.floor(Math.random() * availableMonsterTypes.length)];
        
        // Calculate base stats with floor scaling
        const floorMultiplier = 1 + (gameState.currentFloor - 1) * 0.3;
        const baseStats = {
            life: Math.floor(monsterType.baseStats.life * floorMultiplier),
            str: Math.floor(monsterType.baseStats.str * floorMultiplier),
            int: Math.floor(monsterType.baseStats.int * floorMultiplier),
            agl: Math.floor(monsterType.baseStats.agl * floorMultiplier)
        };
        
        // Apply random multiplier to base stats (70% to 130%)
        const multiplier = CONSTANTS.MULTI_ENEMY.STAT_VARIANCE_MIN + 
            Math.random() * (CONSTANTS.MULTI_ENEMY.STAT_VARIANCE_MAX - CONSTANTS.MULTI_ENEMY.STAT_VARIANCE_MIN);
        
        const enemy = {
            name: statMultiplier > 1 ?
                `Enhanced ${monsterType.name}` :
                monsterType.name,
            type: monsterType.name,
            imagePath: monsterType.imagePath,
            properties: monsterType.properties || [], // Include properties for spiritual_body detection
            behaviorPattern: monsterType.behaviorPattern,
            specialAbilities: monsterType.specialAbilities, // Include special abilities
            maxLife: Math.floor(baseStats.life * multiplier * statMultiplier),
            str: Math.floor(baseStats.str * multiplier * statMultiplier),
            int: Math.floor(baseStats.int * multiplier * statMultiplier),
            agl: Math.floor(baseStats.agl * multiplier * statMultiplier),
            hasRevived: false // Track if zombie has already revived
        };
        
        // Add random AGL bonus of 0-3 points
        enemy.agl += Math.floor(Math.random() * 4);
        
        enemy.life = enemy.maxLife;
        enemy.expReward = Math.floor((enemy.maxLife + enemy.str + enemy.int + enemy.agl) * gameState.currentFloor * 0.5);
        
        return enemy;
    }
    
    generateFinalBoss() {
        const darkLordTemplate = CONSTANTS.BOSS_TYPES.DARK_LORD;
        const eliteMonsterTemplate = CONSTANTS.BOSS_TYPES.ELITE_MONSTER;
        
        // Create Dark Lord (main boss)
        const darkLord = {
            name: darkLordTemplate.name,
            type: 'Dark Lord',
            imagePath: darkLordTemplate.imagePath,
            maxLife: darkLordTemplate.baseStats.life,
            life: darkLordTemplate.baseStats.life,
            str: darkLordTemplate.baseStats.str,
            int: darkLordTemplate.baseStats.int,
            agl: darkLordTemplate.baseStats.agl,
            expReward: 5000,
            isFinalBoss: true,
            specialAttackInterval: darkLordTemplate.specialAttackInterval,
            turnsSinceSpecialAttack: 0
        };
        
        // Create Elite Monster (support boss)
        const eliteMonster = {
            name: eliteMonsterTemplate.name,
            type: 'Elite Monster',
            imagePath: eliteMonsterTemplate.imagePath,
            maxLife: eliteMonsterTemplate.baseStats.life,
            life: eliteMonsterTemplate.baseStats.life,
            str: eliteMonsterTemplate.baseStats.str,
            int: eliteMonsterTemplate.baseStats.int,
            agl: eliteMonsterTemplate.baseStats.agl,
            expReward: 3000,
            isFinalBoss: false
        };
        
        return [darkLord, eliteMonster];
    }
    
    // Enemy respawn system
    respawnEnemies() {
        // Increment respawn count
        gameState.enemyRespawnCount++;
        
        // Calculate maximum spawns: reduce by 1 per respawn, minimum 1
        const maxSpawns = Math.max(1, 
            Math.floor((CONSTANTS.DUNGEON_SIZE * CONSTANTS.DUNGEON_SIZE * CONSTANTS.ENEMY.RESPAWN_CHANCE)) - 
            (gameState.enemyRespawnCount - 1) * CONSTANTS.ENEMY.RESPAWN_COUNT_REDUCTION
        );
        
        let spawnsPlaced = 0;
        const eligibleCells = [];
        
        // Collect all eligible cells
        for (let y = 0; y < CONSTANTS.DUNGEON_SIZE; y++) {
            for (let x = 0; x < CONSTANTS.DUNGEON_SIZE; x++) {
                const cell = gameState.dungeon[y][x];
                if (cell.type === 'floor' && 
                    !cell.enemy && 
                    !cell.enemyRespawned &&
                    !(x === gameState.player.x && y === gameState.player.y)) {
                    eligibleCells.push({ x, y, cell });
                }
            }
        }
        
        // Randomly select cells up to maxSpawns
        while (spawnsPlaced < maxSpawns && eligibleCells.length > 0) {
            const randomIndex = Math.floor(Math.random() * eligibleCells.length);
            const { cell } = eligibleCells[randomIndex];
            
            if (Math.random() < CONSTANTS.ENEMY.RESPAWN_CHANCE) {
                cell.enemy = true;
                cell.enemyRespawned = true;
                spawnsPlaced++;
            }
            
            // Remove this cell from eligible list to avoid duplicate spawns
            eligibleCells.splice(randomIndex, 1);
        }
        
        gameState.dispatchEvent(CONSTANTS.EVENTS.GAME_STATE_CHANGED);
    }
}

// Create global instance
const player = new Player();

// Make available globally for browser
if (typeof window !== 'undefined') {
    window.player = player;
}
